# النعماني ستور - متجر إلكتروني متكامل

## نظرة عامة
النعماني ستور هو تطبيق تجارة إلكترونية متكامل مطور باستخدام Django مع دعم كامل للغة العربية والعملة المصرية. يتميز التطبيق بتصميم عصري ومتجاوب وميزة تتبع الشحن المتقدمة.

## التقنيات المستخدمة
- **Backend**: Django 5.2
- **Frontend**: Django Templates + HTMX + Tailwind CSS + Alpine.js
- **Database**: SQLite (قابل للتغيير إلى PostgreSQL/MySQL)
- **Styling**: Tailwind CSS
- **Icons**: Font Awesome
- **Fonts**: Google Fonts (Cairo, Tajawal)

## الميزات الرئيسية

### 🛍️ التجارة الإلكترونية
- عرض المنتجات مع الصور والأسعار بالجنيه المصري
- تصنيف المنتجات حسب الفئات
- سلة تسوق تفاعلية مع HTMX
- نظام الطلبات المتكامل
- صفحات منتجات مفصلة مع صور متعددة
- نظام الخصومات والعروض

### 🚚 تتبع الشحن المتقدم
- رقم تتبع فريد لكل طلب
- مراحل الشحن التفصيلية:
  - تأكيد الطلب
  - قيد التجهيز
  - تم الشحن
  - في الطريق
  - خرج للتوصيل
  - تم التسليم
- تحديثات الموقع والحالة في الوقت الفعلي
- تاريخ التسليم المتوقع والفعلي
- واجهة تتبع عامة ومخصصة للمستخدمين

### 👤 إدارة المستخدمين
- تسجيل الدخول والخروج
- إنشاء حسابات جديدة
- ملفات شخصية للمستخدمين
- سجل الطلبات الشخصي

### 🎨 التصميم والتجربة
- تصميم عصري ومتجاوب مع Tailwind CSS
- دعم كامل للغة العربية (RTL)
- تفاعلات سلسة مع HTMX و Alpine.js
- متوافق مع جميع الأجهزة (Desktop, Tablet, Mobile)
- واجهة مستخدم بديهية وسهلة الاستخدام

## هيكل المشروع

```
nomany_store/
├── nomany_store/           # إعدادات المشروع الرئيسية
├── products/               # تطبيق المنتجات والفئات
├── cart/                   # تطبيق سلة التسوق
├── orders/                 # تطبيق الطلبات وتتبع الشحن
├── accounts/               # تطبيق إدارة المستخدمين
├── templates/              # قوالب HTML
├── static/                 # الملفات الثابتة
├── media/                  # ملفات الوسائط المرفوعة
└── requirements.txt        # متطلبات المشروع
```

## التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd nomany_store
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv env
# Windows
env\Scripts\activate
# Linux/Mac
source env/bin/activate
```

3. **تثبيت المتطلبات**
```bash
pip install django pillow
```

4. **تطبيق الهجرات**
```bash
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **إنشاء بيانات تجريبية**
```bash
python manage.py create_sample_data
python manage.py create_sample_orders
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

## الاستخدام

### للمطورين
- **لوحة الإدارة**: `http://127.0.0.1:8000/admin/`
- **إدارة المنتجات والفئات**
- **إدارة الطلبات وحالاتها**
- **إدارة تتبع الشحن**

### للمستخدمين
- **الصفحة الرئيسية**: `http://127.0.0.1:8000/`
- **المنتجات**: `http://127.0.0.1:8000/products/`
- **سلة التسوق**: `http://127.0.0.1:8000/cart/`
- **تتبع الطلب**: `http://127.0.0.1:8000/orders/track/`
- **تسجيل الدخول**: `http://127.0.0.1:8000/accounts/login/`

### بيانات تجريبية
- **مستخدم تجريبي**: `testuser` / `testpass123`
- **مدير**: `admin` / `123`
- **منتجات**: 20 منتج في 6 فئات مختلفة
- **طلبات تجريبية**: 3 طلبات مع تتبع شحن

## الصفحات المتاحة

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| الرئيسية | `/` | عرض المنتجات المميزة والفئات |
| المنتجات | `/products/` | قائمة جميع المنتجات مع فلترة |
| تفاصيل المنتج | `/products/<slug>/` | صفحة تفاصيل منتج محدد |
| الفئة | `/products/category/<slug>/` | منتجات فئة محددة |
| سلة التسوق | `/cart/` | عرض وإدارة سلة التسوق |
| الدفع | `/orders/checkout/` | صفحة إتمام الشراء |
| تتبع الطلب | `/orders/track/` | تتبع الطلب برقم التتبع |
| تسجيل الدخول | `/accounts/login/` | تسجيل دخول المستخدم |
| إنشاء حساب | `/accounts/register/` | إنشاء حساب جديد |
| لوحة الإدارة | `/admin/` | إدارة المحتوى |

## المساهمة
نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## الدعم
للدعم الفني أو الاستفسارات:
- Email: <EMAIL>
- Phone: +20 ************

## المطور
تم تطوير هذا المشروع بواسطة فريق النعماني للتطوير.
