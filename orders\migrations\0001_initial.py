# Generated by Django 5.2.1 on 2025-05-28 15:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('confirmed', 'مؤكد'), ('processing', 'قيد التجهيز'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='الاسم الأخير')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('address_line_1', models.CharField(max_length=200, verbose_name='العنوان الأول')),
                ('address_line_2', models.CharField(blank=True, max_length=200, verbose_name='العنوان الثاني')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('governorate', models.CharField(max_length=50, verbose_name='المحافظة')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة الشحن')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الكلي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر الطلب',
                'verbose_name_plural': 'عناصر الطلب',
            },
        ),
    ]
