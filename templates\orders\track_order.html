{% extends 'base/base.html' %}

{% block title %}تتبع الطلب - النعماني ستور{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">تتبع الطلب</h1>
        
        <!-- نموذج البحث -->
        <div class="max-w-md mx-auto mb-8">
            <form method="post" class="space-y-4">
                {% csrf_token %}
                <div>
                    <label for="tracking_number" class="block text-sm font-medium text-gray-700 mb-2">
                        رقم التتبع
                    </label>
                    <input type="text" 
                           id="tracking_number" 
                           name="tracking_number" 
                           value="{{ tracking_number|default:'' }}"
                           placeholder="أدخل رقم التتبع"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                    تتبع الطلب
                </button>
            </form>
        </div>
        
        {% if tracking %}
        <!-- معلومات التتبع -->
        <div class="border-t pt-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900 mb-2">معلومات الشحنة</h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">رقم التتبع:</span> {{ tracking.tracking_number }}</p>
                        <p><span class="font-medium">رقم الطلب:</span> {{ tracking.order.order_number }}</p>
                        <p><span class="font-medium">شركة الشحن:</span> {{ tracking.carrier }}</p>
                        <p><span class="font-medium">الحالة الحالية:</span> 
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                {{ tracking.get_current_status_display }}
                            </span>
                        </p>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900 mb-2">معلومات التسليم</h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">التاريخ المتوقع:</span> 
                            {% if tracking.estimated_delivery %}
                                {{ tracking.estimated_delivery|date:"Y/m/d H:i" }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                        <p><span class="font-medium">تاريخ التسليم الفعلي:</span> 
                            {% if tracking.actual_delivery %}
                                {{ tracking.actual_delivery|date:"Y/m/d H:i" }}
                            {% else %}
                                لم يتم التسليم بعد
                            {% endif %}
                        </p>
                        <p><span class="font-medium">العنوان:</span> 
                            {{ tracking.order.address_line_1 }}, {{ tracking.order.city }}, {{ tracking.order.governorate }}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- مراحل التتبع -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-6">مراحل الشحن</h3>
                
                <!-- شريط التقدم -->
                <div class="relative mb-8">
                    <div class="flex items-center justify-between">
                        {% for status_code, status_name in tracking.TRACKING_STATUS_CHOICES %}
                            {% if status_code in 'order_placed,preparing,shipped,in_transit,out_for_delivery,delivered' %}
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm
                                    {% if tracking.current_status == status_code %}bg-blue-600
                                    {% elif tracking.current_status == 'delivered' or tracking.updates.filter:status=status_code.exists %}bg-green-500
                                    {% else %}bg-gray-300{% endif %}">
                                    <i class="fas fa-check"></i>
                                </div>
                                <span class="text-xs mt-2 text-center max-w-20">{{ status_name }}</span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <div class="absolute top-4 left-0 right-0 h-0.5 bg-gray-300 -z-10"></div>
                </div>
            </div>
            
            <!-- تحديثات التتبع -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-6">تحديثات الشحنة</h3>
                <div class="space-y-4">
                    {% for update in tracking.updates.all %}
                    <div class="flex items-start space-x-4 space-x-reverse p-4 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-truck text-blue-600"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-semibold text-gray-900">{{ update.get_status_display }}</h4>
                                <span class="text-sm text-gray-500">{{ update.timestamp|date:"Y/m/d H:i" }}</span>
                            </div>
                            {% if update.location %}
                            <p class="text-sm text-gray-600 mb-1">
                                <i class="fas fa-map-marker-alt ml-1"></i>
                                {{ update.location }}
                            </p>
                            {% endif %}
                            <p class="text-sm text-gray-700">{{ update.description }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            {% if tracking.notes %}
            <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 class="font-semibold text-yellow-800 mb-2">ملاحظات إضافية</h4>
                <p class="text-yellow-700">{{ tracking.notes }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
