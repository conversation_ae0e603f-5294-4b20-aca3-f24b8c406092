# Generated by Django 5.2.1 on 2025-05-28 15:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('address_line_1', models.CharField(blank=True, max_length=200, verbose_name='العنوان الأول')),
                ('address_line_2', models.CharField(blank=True, max_length=200, verbose_name='العنوان الثاني')),
                ('city', models.CharField(blank=True, max_length=50, verbose_name='المدينة')),
                ('governorate', models.CharField(blank=True, max_length=50, verbose_name='المحافظة')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
    ]
