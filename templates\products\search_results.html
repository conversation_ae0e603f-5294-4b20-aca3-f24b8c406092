{% if products %}
    <div class="max-h-96 overflow-y-auto">
        {% for product in products %}
        <a href="{{ product.get_absolute_url }}" 
           class="flex items-center space-x-3 space-x-reverse p-3 hover:bg-gray-50 border-b">
            {% if product.images.exists %}
                <img src="{{ product.images.first.image.url }}" 
                     alt="{{ product.name }}" 
                     class="w-12 h-12 object-cover rounded">
            {% else %}
                <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                    <i class="fas fa-image text-gray-400"></i>
                </div>
            {% endif %}
            
            <div class="flex-1">
                <h4 class="font-semibold text-sm">{{ product.name }}</h4>
                <p class="text-blue-600 font-bold">{{ product.get_price }} جنيه</p>
            </div>
        </a>
        {% endfor %}
        
        {% if products|length == 10 %}
        <div class="p-3 text-center">
            <a href="{% url 'products:search_products' %}?q={{ query }}" 
               class="text-blue-600 hover:text-blue-800">
                عرض جميع النتائج
            </a>
        </div>
        {% endif %}
    </div>
{% else %}
    {% if query %}
    <div class="p-4 text-center text-gray-500">
        لا توجد نتائج للبحث "{{ query }}"
    </div>
    {% endif %}
{% endif %}
