{% extends 'base/base.html' %}

{% block title %}النعماني ستور - الصفحة الرئيسية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-l from-blue-600 to-purple-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">مرحباً بك في النعماني ستور</h1>
            <p class="text-xl md:text-2xl mb-8">أفضل المنتجات بأسعار منافسة وجودة عالية</p>
            <a href="{% url 'products:product_list' %}" 
               class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                تسوق الآن
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">تسوق حسب الفئة</h2>
            <p class="text-gray-600">اكتشف مجموعتنا المتنوعة من المنتجات</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {% for category in categories %}
            <a href="{% url 'products:category_detail' category.slug %}" 
               class="group text-center hover:transform hover:scale-105 transition duration-300">
                <div class="bg-gray-100 rounded-lg p-6 mb-4 group-hover:bg-blue-50">
                    {% if category.image %}
                        <img src="{{ category.image.url }}" alt="{{ category.name }}" 
                             class="w-16 h-16 mx-auto object-cover rounded-lg">
                    {% else %}
                        <div class="w-16 h-16 mx-auto bg-blue-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-box text-blue-600 text-2xl"></i>
                        </div>
                    {% endif %}
                </div>
                <h3 class="font-semibold text-gray-900 group-hover:text-blue-600">{{ category.name }}</h3>
            </a>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">المنتجات المميزة</h2>
            <p class="text-gray-600">أفضل اختياراتنا لك</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for product in featured_products %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                <a href="{{ product.get_absolute_url }}">
                    {% if product.images.exists %}
                        <img src="{{ product.images.first.image.url }}" 
                             alt="{{ product.name }}" 
                             class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-4xl"></i>
                        </div>
                    {% endif %}
                </a>
                
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href="{{ product.get_absolute_url }}" class="hover:text-blue-600">
                            {{ product.name }}
                        </a>
                    </h3>
                    
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            {% if product.discount_price %}
                                <span class="text-lg font-bold text-red-600">{{ product.discount_price }} جنيه</span>
                                <span class="text-sm text-gray-500 line-through">{{ product.price }} جنيه</span>
                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                    خصم {{ product.get_discount_percentage }}%
                                </span>
                            {% else %}
                                <span class="text-lg font-bold text-gray-900">{{ product.price }} جنيه</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <button hx-post="{% url 'cart:add_to_cart' product.id %}"
                            hx-target="#cart-content"
                            hx-swap="innerHTML"
                            class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                        <i class="fas fa-cart-plus ml-2"></i>
                        أضف للسلة
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-8">
            <a href="{% url 'products:product_list' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                عرض جميع المنتجات
            </a>
        </div>
    </div>
</section>

<!-- Latest Products -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">أحدث المنتجات</h2>
            <p class="text-gray-600">آخر الإضافات إلى متجرنا</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for product in latest_products %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                <a href="{{ product.get_absolute_url }}">
                    {% if product.images.exists %}
                        <img src="{{ product.images.first.image.url }}" 
                             alt="{{ product.name }}" 
                             class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-4xl"></i>
                        </div>
                    {% endif %}
                </a>
                
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href="{{ product.get_absolute_url }}" class="hover:text-blue-600">
                            {{ product.name }}
                        </a>
                    </h3>
                    
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            {% if product.discount_price %}
                                <span class="text-lg font-bold text-red-600">{{ product.discount_price }} جنيه</span>
                                <span class="text-sm text-gray-500 line-through">{{ product.price }} جنيه</span>
                            {% else %}
                                <span class="text-lg font-bold text-gray-900">{{ product.price }} جنيه</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <button hx-post="{% url 'cart:add_to_cart' product.id %}"
                            hx-target="#cart-content"
                            hx-swap="innerHTML"
                            class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                        <i class="fas fa-cart-plus ml-2"></i>
                        أضف للسلة
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shipping-fast text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">شحن سريع</h3>
                <p class="text-gray-600">توصيل سريع لجميع أنحاء مصر</p>
            </div>
            
            <div class="text-center">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">دفع آمن</h3>
                <p class="text-gray-600">طرق دفع متعددة وآمنة</p>
            </div>
            
            <div class="text-center">
                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-headset text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">دعم 24/7</h3>
                <p class="text-gray-600">خدمة عملاء متاحة على مدار الساعة</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
