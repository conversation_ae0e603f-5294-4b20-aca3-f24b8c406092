{% if cart_items %}
    <div class="space-y-4">
        {% for item in cart_items %}
        <div class="flex items-center space-x-4 space-x-reverse border-b pb-4">
            {% if item.product.images.exists %}
                <img src="{{ item.product.images.first.image.url }}" 
                     alt="{{ item.product.name }}" 
                     class="w-16 h-16 object-cover rounded">
            {% else %}
                <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                    <i class="fas fa-image text-gray-400"></i>
                </div>
            {% endif %}
            
            <div class="flex-1">
                <h4 class="font-semibold text-sm">{{ item.product.name }}</h4>
                <p class="text-gray-600 text-sm">{{ item.product.get_price }} جنيه × {{ item.quantity }}</p>
                <p class="font-bold text-blue-600">{{ item.get_total_price }} جنيه</p>
            </div>
            
            <div class="flex items-center space-x-2 space-x-reverse">
                <form hx-post="{% url 'cart:update_cart' item.product.id %}"
                      hx-vals='{"quantity": {{ item.quantity|add:"-1" }}}'
                      hx-target="#cart-content">
                    {% csrf_token %}
                    <button type="submit" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">
                        <i class="fas fa-minus text-xs"></i>
                    </button>
                </form>

                <span class="w-8 text-center">{{ item.quantity }}</span>

                <form hx-post="{% url 'cart:update_cart' item.product.id %}"
                      hx-vals='{"quantity": {{ item.quantity|add:"1" }}}'
                      hx-target="#cart-content">
                    {% csrf_token %}
                    <button type="submit" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">
                        <i class="fas fa-plus text-xs"></i>
                    </button>
                </form>

                <form hx-post="{% url 'cart:remove_from_cart' item.product.id %}"
                      hx-target="#cart-content">
                    {% csrf_token %}
                    <button type="submit" class="text-red-500 hover:text-red-700 mr-2">
                        <i class="fas fa-trash text-sm"></i>
                    </button>
                </form>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="mt-6 pt-4 border-t">
        <div class="flex justify-between items-center mb-4">
            <span class="font-semibold">المجموع:</span>
            <span class="font-bold text-lg text-blue-600">{{ cart.total_price }} جنيه</span>
        </div>
        
        <div class="space-y-2">
            <a href="{% url 'cart:cart_detail' %}" 
               class="block w-full bg-gray-600 text-white text-center py-2 rounded-lg hover:bg-gray-700 transition duration-300">
                عرض السلة
            </a>
            <a href="{% url 'orders:checkout' %}" 
               class="block w-full bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                إتمام الشراء
            </a>
        </div>
    </div>
{% else %}
    <div class="text-center py-8">
        <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-4"></i>
        <p class="text-gray-500">سلة التسوق فارغة</p>
        <a href="{% url 'products:product_list' %}" 
           class="inline-block mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
            تسوق الآن
        </a>
    </div>
{% endif %}
