{% extends 'base/base.html' %}

{% block title %}تسجيل الدخول - النعماني ستور{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                <i class="fas fa-user text-blue-600 text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                تسجيل الدخول إلى حسابك
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                أو
                <a href="{% url 'accounts:register' %}" class="font-medium text-blue-600 hover:text-blue-500">
                    إنشاء حساب جديد
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            
            {% if form.errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-red-800">
                            يرجى تصحيح الأخطاء التالية:
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc list-inside space-y-1">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                    <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="sr-only">اسم المستخدم</label>
                    <input id="{{ form.username.id_for_label }}" 
                           name="{{ form.username.name }}" 
                           type="text" 
                           required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="اسم المستخدم"
                           value="{{ form.username.value|default:'' }}">
                </div>
                <div>
                    <label for="{{ form.password.id_for_label }}" class="sr-only">كلمة المرور</label>
                    <input id="{{ form.password.id_for_label }}" 
                           name="{{ form.password.name }}" 
                           type="password" 
                           required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="كلمة المرور">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember-me" class="mr-2 block text-sm text-gray-900">
                        تذكرني
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                        نسيت كلمة المرور؟
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute right-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                    </span>
                    تسجيل الدخول
                </button>
            </div>
            
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    ليس لديك حساب؟
                    <a href="{% url 'accounts:register' %}" class="font-medium text-blue-600 hover:text-blue-500">
                        سجل الآن
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
