from django.contrib import admin
from .models import Order, OrderItem, ShippingTracking, TrackingUpdate

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'user', 'status', 'total', 'created_at']
    list_filter = ['status', 'created_at', 'governorate']
    search_fields = ['order_number', 'user__username', 'first_name', 'last_name']
    inlines = [OrderItemInline]
    list_editable = ['status']

@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'product', 'quantity', 'price', 'get_total_price']
    list_filter = ['order__created_at']

class TrackingUpdateInline(admin.TabularInline):
    model = TrackingUpdate
    extra = 1

@admin.register(ShippingTracking)
class ShippingTrackingAdmin(admin.ModelAdmin):
    list_display = ['tracking_number', 'order', 'current_status', 'carrier', 'estimated_delivery', 'updated_at']
    list_filter = ['current_status', 'carrier', 'created_at']
    search_fields = ['tracking_number', 'order__order_number']
    inlines = [TrackingUpdateInline]
    list_editable = ['current_status']

@admin.register(TrackingUpdate)
class TrackingUpdateAdmin(admin.ModelAdmin):
    list_display = ['tracking', 'status', 'location', 'timestamp']
    list_filter = ['status', 'timestamp']
    search_fields = ['tracking__tracking_number', 'location', 'description']
