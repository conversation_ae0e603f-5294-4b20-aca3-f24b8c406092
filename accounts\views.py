from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.contrib.auth.models import User
from .models import UserProfile

def register(request):
    """تسجيل مستخدم جديد"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'تم إنشاء الحساب بنجاح!')
            return redirect('home')
    else:
        form = UserCreationForm()

    context = {
        'form': form,
    }
    return render(request, 'accounts/register.html', context)

@login_required
def profile(request):
    """الملف الشخصي للمستخدم"""
    try:
        user_profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(user=request.user)

    context = {
        'user_profile': user_profile,
    }
    return render(request, 'accounts/profile.html', context)

@login_required
def edit_profile(request):
    """تعديل الملف الشخصي"""
    try:
        user_profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(user=request.user)

    if request.method == 'POST':
        # تحديث بيانات المستخدم
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        # تحديث بيانات الملف الشخصي
        user_profile.phone = request.POST.get('phone', '')
        user_profile.address_line_1 = request.POST.get('address_line_1', '')
        user_profile.address_line_2 = request.POST.get('address_line_2', '')
        user_profile.city = request.POST.get('city', '')
        user_profile.governorate = request.POST.get('governorate', '')
        user_profile.postal_code = request.POST.get('postal_code', '')
        user_profile.save()

        messages.success(request, 'تم تحديث الملف الشخصي بنجاح!')
        return redirect('accounts:profile')

    context = {
        'user_profile': user_profile,
    }
    return render(request, 'accounts/edit_profile.html', context)
