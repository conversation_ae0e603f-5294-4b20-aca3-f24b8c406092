{% load tailwind_field %}

{% if field.is_hidden %}
    {{ field }}
{% else %}
    {% if field|is_checkbox %}
        <div id="div_{{ field.auto_id }}" class="{% if wrapper_class %} {{ wrapper_class }}{% endif %}">
            <label for="{{ field.id_for_label }}" class="{% if field.field.required %} requiredField{% endif %}">
                {% tailwind_field field %}
                {{ field.label|safe }}
            </label>
        </div>
    {% else %}
        <div id="div_{{ field.auto_id }}" class="{% if wrapper_class %} {{ wrapper_class }}{% endif %}">
            <label for="{{ field.id_for_label }}" class="sr-only{% if field.field.required %} requiredField{% endif %}">
                {{ field.label|safe }}
            </label>
            {% tailwind_field field 'placeholder' field.label %}
        </div>
    {% endif %}
{% endif %}
