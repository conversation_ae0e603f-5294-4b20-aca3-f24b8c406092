{% extends 'base/base.html' %}

{% block title %}إنشاء حساب جديد - النعماني ستور{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                <i class="fas fa-user-plus text-green-600 text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                إنشاء حساب جديد
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                أو
                <a href="{% url 'accounts:login' %}" class="font-medium text-blue-600 hover:text-blue-500">
                    تسجيل الدخول إلى حسابك
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            
            {% if form.errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-red-800">
                            يرجى تصحيح الأخطاء التالية:
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc list-inside space-y-1">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                    <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="space-y-4">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">اسم المستخدم</label>
                    <input id="{{ form.username.id_for_label }}" 
                           name="{{ form.username.name }}" 
                           type="text" 
                           required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="اختر اسم مستخدم"
                           value="{{ form.username.value|default:'' }}">
                    {% if form.username.help_text %}
                    <p class="mt-1 text-xs text-gray-500">{{ form.username.help_text }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">كلمة المرور</label>
                    <input id="{{ form.password1.id_for_label }}" 
                           name="{{ form.password1.name }}" 
                           type="password" 
                           required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="كلمة المرور">
                    {% if form.password1.help_text %}
                    <p class="mt-1 text-xs text-gray-500">{{ form.password1.help_text }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">تأكيد كلمة المرور</label>
                    <input id="{{ form.password2.id_for_label }}" 
                           name="{{ form.password2.name }}" 
                           type="password" 
                           required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="أعد كتابة كلمة المرور">
                    {% if form.password2.help_text %}
                    <p class="mt-1 text-xs text-gray-500">{{ form.password2.help_text }}</p>
                    {% endif %}
                </div>
            </div>

            <div class="flex items-center">
                <input id="terms" name="terms" type="checkbox" required class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="terms" class="mr-2 block text-sm text-gray-900">
                    أوافق على 
                    <a href="#" class="text-blue-600 hover:text-blue-500">الشروط والأحكام</a>
                    و
                    <a href="#" class="text-blue-600 hover:text-blue-500">سياسة الخصوصية</a>
                </label>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <span class="absolute right-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-user-plus text-green-500 group-hover:text-green-400"></i>
                    </span>
                    إنشاء الحساب
                </button>
            </div>
            
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    لديك حساب بالفعل؟
                    <a href="{% url 'accounts:login' %}" class="font-medium text-blue-600 hover:text-blue-500">
                        سجل دخولك
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}
