from django.core.management.base import BaseCommand
from products.models import Product, Category

class Command(BaseCommand):
    help = 'إصلاح المنتجات والفئات التي بدون slug يدوياً'

    def handle(self, *args, **options):
        # إصلاح الفئات
        category_mappings = {
            'الإلكترونيات': 'electronics',
            'الملابس': 'clothing',
            'المنزل والحديقة': 'home-garden',
            'الكتب': 'books',
            'الرياضة': 'sports',
            'الجمال والعناية': 'beauty-care',
        }

        for arabic_name, english_slug in category_mappings.items():
            try:
                category = Category.objects.get(name=arabic_name)
                if not category.slug or category.slug.startswith('-'):
                    category.slug = english_slug
                    category.save()
                    self.stdout.write(f'تم إصلاح slug للفئة: {category.name} -> {english_slug}')
            except Category.DoesNotExist:
                pass

        # إصلاح المنتجات
        product_mappings = {
            'هاتف ذكي سامسونج جالاكسي': 'samsung-galaxy-smartphone',
            'لابتوب ديل انسبايرون': 'dell-inspiron-laptop',
            'سماعات بلوتوث': 'bluetooth-headphones',
            'شاشة كمبيوتر 24 بوصة': 'computer-monitor-24-inch',
            'قميص قطني رجالي': 'mens-cotton-shirt',
            'فستان صيفي نسائي': 'womens-summer-dress',
            'جاكيت شتوي': 'winter-jacket',
            'حذاء رياضي': 'sports-shoes',
            'طقم أواني طبخ': 'cooking-utensils-set',
            'مكنسة كهربائية': 'vacuum-cleaner',
            'طاولة خشبية': 'wooden-table',
            'كتاب البرمجة بـ Python': 'python-programming-book',
            'رواية مئة عام من العزلة': 'hundred-years-solitude-novel',
            'كتاب إدارة الأعمال': 'business-management-book',
            'كرة قدم أديداس': 'adidas-football',
            'دراجة هوائية': 'bicycle',
            'حقيبة رياضية': 'sports-bag',
            'كريم مرطب للوجه': 'face-moisturizer',
            'شامبو طبيعي': 'natural-shampoo',
            'عطر رجالي فاخر': 'luxury-mens-perfume',
        }

        for arabic_name, english_slug in product_mappings.items():
            try:
                product = Product.objects.get(name=arabic_name)
                if not product.slug or product.slug.startswith('-'):
                    product.slug = english_slug
                    product.save()
                    self.stdout.write(f'تم إصلاح slug للمنتج: {product.name} -> {english_slug}')
            except Product.DoesNotExist:
                pass

        self.stdout.write(
            self.style.SUCCESS('تم إصلاح جميع المنتجات والفئات')
        )
