{% extends 'base/base.html' %}

{% block title %}المنتجات - النعماني ستور{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- الشريط الجانبي للفلترة -->
        <div class="lg:w-1/4">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">تصفية المنتجات</h3>
                
                <!-- فلترة حسب الفئة -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">الفئات</h4>
                    <div class="space-y-2">
                        <a href="{% url 'products:product_list' %}" 
                           class="block text-sm text-gray-600 hover:text-blue-600 {% if not current_category %}font-semibold text-blue-600{% endif %}">
                            جميع الفئات
                        </a>
                        {% for category in categories %}
                        <a href="{% url 'products:product_list' %}?category={{ category.slug }}" 
                           class="block text-sm text-gray-600 hover:text-blue-600 {% if current_category == category.slug %}font-semibold text-blue-600{% endif %}">
                            {{ category.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- فلترة حسب السعر -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">نطاق السعر</h4>
                    <form method="get" class="space-y-3">
                        {% if current_category %}
                        <input type="hidden" name="category" value="{{ current_category }}">
                        {% endif %}
                        <input type="hidden" name="sort" value="{{ current_sort }}">
                        
                        <div class="flex space-x-2 space-x-reverse">
                            <input type="number" name="min_price" placeholder="من" 
                                   value="{{ request.GET.min_price }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            <input type="number" name="max_price" placeholder="إلى" 
                                   value="{{ request.GET.max_price }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                        </div>
                        <button type="submit" 
                                class="w-full bg-blue-600 text-white py-2 rounded text-sm hover:bg-blue-700">
                            تطبيق
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="lg:w-3/4">
            <!-- شريط الترتيب -->
            <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                <div class="flex items-center justify-between">
                    <p class="text-gray-600">
                        عرض {{ page_obj.start_index }}-{{ page_obj.end_index }} من {{ page_obj.paginator.count }} منتج
                    </p>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <label class="text-sm text-gray-600">ترتيب حسب:</label>
                        <select onchange="location.href=this.value" 
                                class="border border-gray-300 rounded px-3 py-1 text-sm">
                            <option value="{% url 'products:product_list' %}?sort=-created_at{% if current_category %}&category={{ current_category }}{% endif %}" 
                                    {% if current_sort == '-created_at' %}selected{% endif %}>
                                الأحدث
                            </option>
                            <option value="{% url 'products:product_list' %}?sort=price{% if current_category %}&category={{ current_category }}{% endif %}" 
                                    {% if current_sort == 'price' %}selected{% endif %}>
                                السعر: من الأقل للأعلى
                            </option>
                            <option value="{% url 'products:product_list' %}?sort=-price{% if current_category %}&category={{ current_category }}{% endif %}" 
                                    {% if current_sort == '-price' %}selected{% endif %}>
                                السعر: من الأعلى للأقل
                            </option>
                            <option value="{% url 'products:product_list' %}?sort=name{% if current_category %}&category={{ current_category }}{% endif %}" 
                                    {% if current_sort == 'name' %}selected{% endif %}>
                                الاسم: أ-ي
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- شبكة المنتجات -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for product in page_obj %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <a href="{{ product.get_absolute_url }}">
                        {% if product.images.exists %}
                            <img src="{{ product.images.first.image.url }}" 
                                 alt="{{ product.name }}" 
                                 class="w-full h-48 object-cover">
                        {% else %}
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}
                    </a>
                    
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                            <a href="{{ product.get_absolute_url }}" class="hover:text-blue-600">
                                {{ product.name }}
                            </a>
                        </h3>
                        
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ product.description|truncatewords:15 }}</p>
                        
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                {% if product.discount_price %}
                                    <span class="text-lg font-bold text-red-600">{{ product.discount_price }} جنيه</span>
                                    <span class="text-sm text-gray-500 line-through">{{ product.price }} جنيه</span>
                                    <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                        خصم {{ product.get_discount_percentage }}%
                                    </span>
                                {% else %}
                                    <span class="text-lg font-bold text-gray-900">{{ product.price }} جنيه</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">
                                {% if product.stock > 0 %}
                                    متوفر ({{ product.stock }})
                                {% else %}
                                    غير متوفر
                                {% endif %}
                            </span>
                            
                            {% if product.stock > 0 %}
                            <form hx-post="{% url 'cart:add_to_cart' product.id %}"
                                  hx-target="#cart-content"
                                  hx-swap="innerHTML">
                                {% csrf_token %}
                                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-sm">
                                    <i class="fas fa-cart-plus ml-1"></i>
                                    أضف للسلة
                                </button>
                            </form>
                            {% else %}
                            <button disabled 
                                    class="bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed text-sm">
                                غير متوفر
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-box-open text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد منتجات</h3>
                    <p class="text-gray-500">لم يتم العثور على منتجات تطابق معايير البحث</p>
                </div>
                {% endfor %}
            </div>
            
            <!-- التنقل بين الصفحات -->
            {% if page_obj.has_other_pages %}
            <div class="mt-8 flex justify-center">
                <nav class="flex items-center space-x-2 space-x-reverse">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            السابق
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 bg-blue-600 text-white rounded-lg">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}" 
                               class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            التالي
                        </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
