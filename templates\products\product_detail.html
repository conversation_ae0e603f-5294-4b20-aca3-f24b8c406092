{% extends 'base/base.html' %}

{% block title %}{{ product.name }} - النعماني ستور{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- مسار التنقل -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li class="inline-flex items-center">
                <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">الرئيسية</a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{% url 'products:product_list' %}" class="text-gray-700 hover:text-blue-600">المنتجات</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{% url 'products:category_detail' product.category.slug %}" class="text-gray-700 hover:text-blue-600">{{ product.category.name }}</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <span class="text-gray-500">{{ product.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- صور المنتج -->
        <div class="space-y-4">
            {% if product.images.exists %}
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <img src="{{ product.images.first.image.url }}" 
                         alt="{{ product.name }}" 
                         class="w-full h-full object-cover"
                         id="main-image">
                </div>
                
                {% if product.images.count > 1 %}
                <div class="grid grid-cols-4 gap-2">
                    {% for image in product.images.all %}
                    <button onclick="document.getElementById('main-image').src='{{ image.image.url }}'"
                            class="aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-500 transition duration-200">
                        <img src="{{ image.image.url }}" 
                             alt="{{ product.name }}" 
                             class="w-full h-full object-cover">
                    </button>
                    {% endfor %}
                </div>
                {% endif %}
            {% else %}
                <div class="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-image text-gray-400 text-6xl"></i>
                </div>
            {% endif %}
        </div>

        <!-- تفاصيل المنتج -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ product.name }}</h1>
                <p class="text-gray-600">{{ product.category.name }}</p>
            </div>

            <!-- السعر -->
            <div class="flex items-center space-x-4 space-x-reverse">
                {% if product.discount_price %}
                    <span class="text-3xl font-bold text-red-600">{{ product.discount_price }} جنيه</span>
                    <span class="text-xl text-gray-500 line-through">{{ product.price }} جنيه</span>
                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">
                        خصم {{ product.get_discount_percentage }}%
                    </span>
                {% else %}
                    <span class="text-3xl font-bold text-gray-900">{{ product.price }} جنيه</span>
                {% endif %}
            </div>

            <!-- حالة التوفر -->
            <div class="flex items-center space-x-2 space-x-reverse">
                {% if product.stock > 0 %}
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span class="text-green-600 font-semibold">متوفر ({{ product.stock }} قطعة)</span>
                {% else %}
                    <i class="fas fa-times-circle text-red-500"></i>
                    <span class="text-red-600 font-semibold">غير متوفر</span>
                {% endif %}
            </div>

            <!-- الوصف -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">وصف المنتج</h3>
                <p class="text-gray-700 leading-relaxed">{{ product.description }}</p>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="space-y-4">
                {% if product.stock > 0 %}
                <div class="flex items-center space-x-4 space-x-reverse" x-data="{ quantity: 1 }">
                    <!-- اختيار الكمية -->
                    <div class="flex items-center border border-gray-300 rounded-lg">
                        <button @click="quantity = Math.max(1, quantity - 1)" 
                                class="px-3 py-2 hover:bg-gray-100 transition duration-200">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" x-model="quantity" min="1" max="{{ product.stock }}"
                               class="w-16 text-center border-0 focus:ring-0">
                        <button @click="quantity = Math.min({{ product.stock }}, quantity + 1)" 
                                class="px-3 py-2 hover:bg-gray-100 transition duration-200">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>

                    <!-- زر الإضافة للسلة -->
                    <button hx-post="{% url 'cart:add_to_cart' product.id %}"
                            hx-target="#cart-content"
                            hx-swap="innerHTML"
                            class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300 font-semibold">
                        <i class="fas fa-cart-plus ml-2"></i>
                        أضف إلى السلة
                    </button>
                </div>
                {% else %}
                <button disabled 
                        class="w-full bg-gray-400 text-white py-3 px-6 rounded-lg cursor-not-allowed font-semibold">
                    غير متوفر حالياً
                </button>
                {% endif %}

                <!-- أزرار إضافية -->
                <div class="flex space-x-4 space-x-reverse">
                    <button class="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition duration-300">
                        <i class="fas fa-heart ml-2"></i>
                        إضافة للمفضلة
                    </button>
                    <button class="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition duration-300">
                        <i class="fas fa-share ml-2"></i>
                        مشاركة
                    </button>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="border-t pt-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-truck text-blue-600"></i>
                        <span>شحن مجاني للطلبات أكثر من 500 جنيه</span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-undo text-blue-600"></i>
                        <span>إمكانية الإرجاع خلال 14 يوم</span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-shield-alt text-blue-600"></i>
                        <span>ضمان الجودة</span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-headset text-blue-600"></i>
                        <span>دعم فني 24/7</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المنتجات ذات الصلة -->
    {% if related_products %}
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">منتجات مشابهة</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for related_product in related_products %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                <a href="{{ related_product.get_absolute_url }}">
                    {% if related_product.images.exists %}
                        <img src="{{ related_product.images.first.image.url }}" 
                             alt="{{ related_product.name }}" 
                             class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-3xl"></i>
                        </div>
                    {% endif %}
                </a>
                
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href="{{ related_product.get_absolute_url }}" class="hover:text-blue-600">
                            {{ related_product.name }}
                        </a>
                    </h3>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            {% if related_product.discount_price %}
                                <span class="text-lg font-bold text-red-600">{{ related_product.discount_price }} جنيه</span>
                                <span class="text-sm text-gray-500 line-through block">{{ related_product.price }} جنيه</span>
                            {% else %}
                                <span class="text-lg font-bold text-gray-900">{{ related_product.price }} جنيه</span>
                            {% endif %}
                        </div>
                        
                        {% if related_product.stock > 0 %}
                        <button hx-post="{% url 'cart:add_to_cart' related_product.id %}"
                                hx-target="#cart-content"
                                hx-swap="innerHTML"
                                class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition duration-300">
                            <i class="fas fa-cart-plus"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
