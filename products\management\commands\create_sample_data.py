from django.core.management.base import BaseCommand
from products.models import Category, Product, ProductImage
from decimal import Decimal

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للمتجر'

    def handle(self, *args, **options):
        # إنشاء الفئات
        categories_data = [
            {'name': 'الإلكترونيات', 'description': 'أجهزة إلكترونية متنوعة'},
            {'name': 'الملابس', 'description': 'ملابس رجالية ونسائية'},
            {'name': 'المنزل والحديقة', 'description': 'أدوات منزلية ومستلزمات الحديقة'},
            {'name': 'الكتب', 'description': 'كتب متنوعة في جميع المجالات'},
            {'name': 'الرياضة', 'description': 'معدات ومستلزمات رياضية'},
            {'name': 'الجمال والعناية', 'description': 'منتجات التجميل والعناية الشخصية'},
        ]

        categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories.append(category)
            if created:
                self.stdout.write(f'تم إنشاء فئة: {category.name}')

        # إنشاء المنتجات
        products_data = [
            # الإلكترونيات
            {'name': 'هاتف ذكي سامسونج جالاكسي', 'category': 0, 'price': 8500, 'discount_price': 7500, 'stock': 25, 'is_featured': True},
            {'name': 'لابتوب ديل انسبايرون', 'category': 0, 'price': 15000, 'stock': 10, 'is_featured': True},
            {'name': 'سماعات بلوتوث', 'category': 0, 'price': 450, 'discount_price': 350, 'stock': 50},
            {'name': 'شاشة كمبيوتر 24 بوصة', 'category': 0, 'price': 3200, 'stock': 15},
            
            # الملابس
            {'name': 'قميص قطني رجالي', 'category': 1, 'price': 180, 'stock': 30, 'is_featured': True},
            {'name': 'فستان صيفي نسائي', 'category': 1, 'price': 320, 'discount_price': 250, 'stock': 20},
            {'name': 'جاكيت شتوي', 'category': 1, 'price': 650, 'stock': 15},
            {'name': 'حذاء رياضي', 'category': 1, 'price': 420, 'stock': 25},
            
            # المنزل والحديقة
            {'name': 'طقم أواني طبخ', 'category': 2, 'price': 850, 'discount_price': 700, 'stock': 12},
            {'name': 'مكنسة كهربائية', 'category': 2, 'price': 1200, 'stock': 8, 'is_featured': True},
            {'name': 'طاولة خشبية', 'category': 2, 'price': 2500, 'stock': 5},
            
            # الكتب
            {'name': 'كتاب البرمجة بـ Python', 'category': 3, 'price': 120, 'stock': 40},
            {'name': 'رواية مئة عام من العزلة', 'category': 3, 'price': 85, 'stock': 30},
            {'name': 'كتاب إدارة الأعمال', 'category': 3, 'price': 150, 'discount_price': 120, 'stock': 25},
            
            # الرياضة
            {'name': 'كرة قدم أديداس', 'category': 4, 'price': 280, 'stock': 20},
            {'name': 'دراجة هوائية', 'category': 4, 'price': 3500, 'discount_price': 3000, 'stock': 6, 'is_featured': True},
            {'name': 'حقيبة رياضية', 'category': 4, 'price': 180, 'stock': 35},
            
            # الجمال والعناية
            {'name': 'كريم مرطب للوجه', 'category': 5, 'price': 95, 'stock': 45},
            {'name': 'شامبو طبيعي', 'category': 5, 'price': 65, 'discount_price': 50, 'stock': 60},
            {'name': 'عطر رجالي فاخر', 'category': 5, 'price': 450, 'stock': 18, 'is_featured': True},
        ]

        for product_data in products_data:
            category = categories[product_data['category']]
            
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults={
                    'category': category,
                    'description': f'وصف تفصيلي لـ {product_data["name"]}. منتج عالي الجودة بأفضل الأسعار.',
                    'price': Decimal(str(product_data['price'])),
                    'discount_price': Decimal(str(product_data.get('discount_price', 0))) if product_data.get('discount_price') else None,
                    'stock': product_data['stock'],
                    'is_featured': product_data.get('is_featured', False),
                }
            )
            
            if created:
                self.stdout.write(f'تم إنشاء منتج: {product.name}')

        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )
