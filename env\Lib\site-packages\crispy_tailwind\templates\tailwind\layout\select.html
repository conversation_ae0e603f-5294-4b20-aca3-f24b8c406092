{% load crispy_forms_filters %}
{% load tailwind_filters %}
{% load l10n %}

<div class="relative">
    <select class="bg-white focus:outline-none border {% if field.errors %}border-red-500 {% else %}border-gray-300 {% endif %}rounded-lg py-2 px-4 block w-full appearance-none leading-normal text-gray-700" name="{{ field.html_name }}" {{ field|build_attrs }}>
        {% for group, options, index in field|optgroups %}
            {% if group %}<optgroup label="{{ group }}">{% endif %}
            {% for option in options %}
                {% include "tailwind/layout/select_option.html" %}
            {% endfor %}
            {% if group %}</optgroup>{% endif %}
        {% endfor %}
    </select>
    {% if not field.field.widget.allow_multiple_selected %}
        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
        </div>
    {% endif %}
</div>
