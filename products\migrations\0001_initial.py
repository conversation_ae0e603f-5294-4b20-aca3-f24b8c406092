# Generated by Django 5.2.1 on 2025-05-28 15:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, null=True, upload_to='categories/', verbose_name='صورة الفئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('description', models.TextField(verbose_name='الوصف')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر (جنيه مصري)')),
                ('discount_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الخصم')),
                ('stock', models.PositiveIntegerField(default=0, verbose_name='الكمية المتاحة')),
                ('is_available', models.BooleanField(default=True, verbose_name='متاح')),
                ('is_featured', models.BooleanField(default=False, verbose_name='منتج مميز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/', verbose_name='الصورة')),
                ('alt_text', models.CharField(blank=True, max_length=200, verbose_name='النص البديل')),
                ('is_main', models.BooleanField(default=False, verbose_name='الصورة الرئيسية')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة المنتج',
                'verbose_name_plural': 'صور المنتجات',
            },
        ),
    ]
