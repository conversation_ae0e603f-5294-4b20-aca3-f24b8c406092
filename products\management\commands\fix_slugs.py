from django.core.management.base import BaseCommand
from products.models import Product, Category
from django.utils.text import slugify

class Command(BaseCommand):
    help = 'إصلاح المنتجات والفئات التي بدون slug'

    def handle(self, *args, **options):
        # إصلاح الفئات
        categories_fixed = 0
        for category in Category.objects.filter(slug=''):
            base_slug = slugify(category.name)
            slug = base_slug
            counter = 1
            while Category.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            category.slug = slug
            category.save()
            categories_fixed += 1
            self.stdout.write(f'تم إصلاح slug للفئة: {category.name} -> {slug}')

        # إصلاح المنتجات
        products_fixed = 0
        for product in Product.objects.filter(slug=''):
            base_slug = slugify(product.name)
            slug = base_slug
            counter = 1
            while Product.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            product.slug = slug
            product.save()
            products_fixed += 1
            self.stdout.write(f'تم إصلاح slug للمنتج: {product.name} -> {slug}')

        self.stdout.write(
            self.style.SUCCESS(f'تم إصلاح {categories_fixed} فئة و {products_fixed} منتج')
        )
