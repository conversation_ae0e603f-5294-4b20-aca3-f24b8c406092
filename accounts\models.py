from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    phone = models.CharField(max_length=20, blank=True, verbose_name="رقم الهاتف")
    birth_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الميلاد")

    # Address information
    address_line_1 = models.CharField(max_length=200, blank=True, verbose_name="العنوان الأول")
    address_line_2 = models.Char<PERSON>ield(max_length=200, blank=True, verbose_name="العنوان الثاني")
    city = models.CharField(max_length=50, blank=True, verbose_name="المدينة")
    governorate = models.Char<PERSON>ield(max_length=50, blank=True, verbose_name="المحافظة")
    postal_code = models.Char<PERSON><PERSON>(max_length=10, blank=True, verbose_name="الرمز البريدي")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def __str__(self):
        return f"ملف {self.user.username}"

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    instance.userprofile.save()
