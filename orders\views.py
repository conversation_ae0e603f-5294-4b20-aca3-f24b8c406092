from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from .models import Order, OrderItem, ShippingTracking, TrackingUpdate
from cart.models import Cart
from datetime import datetime, timedelta

@login_required
def order_list(request):
    """قائمة طلبات المستخدم"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')

    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }
    return render(request, 'orders/order_list.html', context)

@login_required
def order_detail(request, order_number):
    """تفاصيل الطلب"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    context = {
        'order': order,
    }
    return render(request, 'orders/order_detail.html', context)

@login_required
def checkout(request):
    """صفحة الدفع"""
    try:
        cart = Cart.objects.get(user=request.user)
        cart_items = cart.items.all()

        if not cart_items:
            messages.warning(request, 'سلة التسوق فارغة')
            return redirect('cart:cart_detail')

    except Cart.DoesNotExist:
        messages.warning(request, 'سلة التسوق فارغة')
        return redirect('cart:cart_detail')

    context = {
        'cart': cart,
        'cart_items': cart_items,
    }
    return render(request, 'orders/checkout.html', context)

@login_required
def create_order(request):
    """إنشاء طلب جديد"""
    if request.method == 'POST':
        try:
            cart = Cart.objects.get(user=request.user)
            cart_items = cart.items.all()

            if not cart_items:
                messages.error(request, 'سلة التسوق فارغة')
                return redirect('cart:cart_detail')

            # إنشاء الطلب
            order = Order.objects.create(
                user=request.user,
                first_name=request.POST.get('first_name'),
                last_name=request.POST.get('last_name'),
                email=request.POST.get('email'),
                phone=request.POST.get('phone'),
                address_line_1=request.POST.get('address_line_1'),
                address_line_2=request.POST.get('address_line_2', ''),
                city=request.POST.get('city'),
                governorate=request.POST.get('governorate'),
                postal_code=request.POST.get('postal_code', ''),
                subtotal=cart.total_price,
                shipping_cost=50,  # تكلفة شحن ثابتة
                total=cart.total_price + 50,
            )

            # إنشاء عناصر الطلب
            for cart_item in cart_items:
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    price=cart_item.product.get_price,
                )

            # إنشاء تتبع الشحن
            tracking = ShippingTracking.objects.create(
                order=order,
                estimated_delivery=datetime.now() + timedelta(days=3)
            )

            # إضافة أول تحديث للتتبع
            TrackingUpdate.objects.create(
                tracking=tracking,
                status='order_placed',
                location='القاهرة - مركز التوزيع الرئيسي',
                description='تم تأكيد الطلب وبدء عملية التجهيز'
            )

            # مسح السلة
            cart_items.delete()

            messages.success(request, f'تم إنشاء الطلب بنجاح. رقم الطلب: {order.order_number}')
            return redirect('orders:order_detail', order_number=order.order_number)

        except Cart.DoesNotExist:
            messages.error(request, 'سلة التسوق فارغة')
            return redirect('cart:cart_detail')
        except Exception as e:
            messages.error(request, 'حدث خطأ أثناء إنشاء الطلب')
            return redirect('orders:checkout')

    return redirect('orders:checkout')

def track_order(request, tracking_number=None):
    """تتبع الطلب"""
    tracking = None

    if tracking_number:
        try:
            tracking = ShippingTracking.objects.get(tracking_number=tracking_number)
        except ShippingTracking.DoesNotExist:
            messages.error(request, 'رقم التتبع غير صحيح')

    if request.method == 'POST':
        tracking_number = request.POST.get('tracking_number')
        if tracking_number:
            return redirect('orders:track_order', tracking_number=tracking_number)

    context = {
        'tracking': tracking,
        'tracking_number': tracking_number,
    }
    return render(request, 'orders/track_order.html', context)

@login_required
def my_order_tracking(request, order_number):
    """تتبع طلب المستخدم"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    try:
        tracking = order.tracking
    except ShippingTracking.DoesNotExist:
        messages.error(request, 'لا يوجد معلومات تتبع لهذا الطلب')
        return redirect('orders:order_detail', order_number=order_number)

    context = {
        'tracking': tracking,
        'order': order,
    }
    return render(request, 'orders/my_order_tracking.html', context)
