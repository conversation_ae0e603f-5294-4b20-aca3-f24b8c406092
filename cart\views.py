from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from products.models import Product
from .models import Cart, CartItem

def get_or_create_cart(user):
    """الحصول على السلة أو إنشاؤها"""
    cart, created = Cart.objects.get_or_create(user=user)
    return cart

def cart_detail(request):
    """عرض تفاصيل السلة"""
    if request.user.is_authenticated:
        cart = get_or_create_cart(request.user)
        cart_items = cart.items.all()
    else:
        cart_items = []
        cart = None

    context = {
        'cart': cart,
        'cart_items': cart_items,
    }

    if request.headers.get('HX-Request'):
        return render(request, 'cart/cart_detail_partial.html', context)

    return render(request, 'cart/cart_detail.html', context)

@require_POST
def add_to_cart(request, product_id):
    """إضافة منتج للسلة"""
    if not request.user.is_authenticated:
        if request.headers.get('HX-Request'):
            return render(request, 'cart/login_required.html')
        return redirect('accounts:login')

    product = get_object_or_404(Product, id=product_id, is_available=True)
    cart = get_or_create_cart(request.user)

    cart_item, created = CartItem.objects.get_or_create(
        cart=cart,
        product=product,
        defaults={'quantity': 1}
    )

    if not created:
        cart_item.quantity += 1
        cart_item.save()

    if request.headers.get('HX-Request'):
        cart_items = cart.items.all()
        return render(request, 'cart/cart_detail_partial.html', {
            'cart': cart,
            'cart_items': cart_items,
        })

    return redirect('cart:cart_detail')

@login_required
@require_POST
def remove_from_cart(request, product_id):
    """إزالة منتج من السلة"""
    product = get_object_or_404(Product, id=product_id)
    cart = get_or_create_cart(request.user)

    try:
        cart_item = CartItem.objects.get(cart=cart, product=product)
        cart_item.delete()
    except CartItem.DoesNotExist:
        pass

    if request.headers.get('HX-Request'):
        cart_items = cart.items.all()
        return render(request, 'cart/cart_detail_partial.html', {
            'cart': cart,
            'cart_items': cart_items,
        })

    return redirect('cart:cart_detail')

@login_required
@require_POST
def update_cart(request, product_id):
    """تحديث كمية المنتج في السلة"""
    product = get_object_or_404(Product, id=product_id)
    cart = get_or_create_cart(request.user)
    quantity = int(request.POST.get('quantity', 1))

    if quantity <= 0:
        return remove_from_cart(request, product_id)

    try:
        cart_item = CartItem.objects.get(cart=cart, product=product)
        cart_item.quantity = quantity
        cart_item.save()
    except CartItem.DoesNotExist:
        pass

    if request.headers.get('HX-Request'):
        cart_items = cart.items.all()
        return render(request, 'cart/cart_detail_partial.html', {
            'cart': cart,
            'cart_items': cart_items,
        })

    return redirect('cart:cart_detail')

@login_required
def clear_cart(request):
    """مسح السلة"""
    cart = get_or_create_cart(request.user)
    cart.items.all().delete()

    if request.headers.get('HX-Request'):
        return render(request, 'cart/cart_detail_partial.html', {
            'cart': cart,
            'cart_items': [],
        })

    return redirect('cart:cart_detail')
