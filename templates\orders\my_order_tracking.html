{% extends 'base/base.html' %}

{% block title %}تتبع الطلب #{{ order.order_number }} - النعماني ستور{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- رأس الصفحة -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-3xl font-bold text-gray-900">تتبع الطلب #{{ order.order_number }}</h1>
            <a href="{% url 'orders:order_detail' order.order_number %}" 
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-300">
                تفاصيل الطلب
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h3 class="font-semibold text-blue-900 mb-2">رقم التتبع</h3>
                <p class="text-blue-700 font-mono">{{ tracking.tracking_number }}</p>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <h3 class="font-semibold text-green-900 mb-2">الحالة الحالية</h3>
                <p class="text-green-700">{{ tracking.get_current_status_display }}</p>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
                <h3 class="font-semibold text-purple-900 mb-2">التسليم المتوقع</h3>
                <p class="text-purple-700">
                    {% if tracking.estimated_delivery %}
                        {{ tracking.estimated_delivery|date:"Y/m/d" }}
                    {% else %}
                        غير محدد
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <!-- مراحل الشحن -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">مراحل الشحن</h2>
        
        <div class="relative">
            <!-- الخط الزمني -->
            <div class="absolute right-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            <div class="space-y-6">
                {% for status_code, status_name in tracking.TRACKING_STATUS_CHOICES %}
                    {% if status_code in 'order_placed,preparing,shipped,in_transit,out_for_delivery,delivered' %}
                    <div class="relative flex items-center">
                        <!-- النقطة -->
                        <div class="relative z-10 w-8 h-8 rounded-full flex items-center justify-center
                            {% if tracking.current_status == status_code %}bg-blue-600 text-white
                            {% elif tracking.updates.filter:status=status_code.exists %}bg-green-500 text-white
                            {% else %}bg-gray-300 text-gray-600{% endif %}">
                            {% if tracking.current_status == status_code %}
                                <i class="fas fa-spinner fa-spin"></i>
                            {% elif tracking.updates.filter:status=status_code.exists %}
                                <i class="fas fa-check"></i>
                            {% else %}
                                <i class="fas fa-circle text-xs"></i>
                            {% endif %}
                        </div>
                        
                        <!-- المحتوى -->
                        <div class="mr-6 flex-1">
                            <h3 class="font-semibold text-gray-900">{{ status_name }}</h3>
                            {% for update in tracking.updates.all %}
                                {% if update.status == status_code %}
                                <p class="text-sm text-gray-600 mt-1">{{ update.timestamp|date:"Y/m/d H:i" }}</p>
                                {% if update.location %}
                                <p class="text-sm text-gray-500">{{ update.location }}</p>
                                {% endif %}
                                <p class="text-sm text-gray-700 mt-1">{{ update.description }}</p>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- تفاصيل الشحنة -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- معلومات الشحن -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4">معلومات الشحن</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">شركة الشحن:</span>
                    <span class="font-medium">{{ tracking.carrier }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">تاريخ الشحن:</span>
                    <span class="font-medium">{{ tracking.created_at|date:"Y/m/d H:i" }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">آخر تحديث:</span>
                    <span class="font-medium">{{ tracking.updated_at|date:"Y/m/d H:i" }}</span>
                </div>
                {% if tracking.actual_delivery %}
                <div class="flex justify-between">
                    <span class="text-gray-600">تاريخ التسليم:</span>
                    <span class="font-medium text-green-600">{{ tracking.actual_delivery|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- عنوان التسليم -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4">عنوان التسليم</h3>
            <div class="space-y-2">
                <p class="font-medium">{{ order.first_name }} {{ order.last_name }}</p>
                <p class="text-gray-600">{{ order.phone }}</p>
                <p class="text-gray-600">{{ order.address_line_1 }}</p>
                {% if order.address_line_2 %}
                <p class="text-gray-600">{{ order.address_line_2 }}</p>
                {% endif %}
                <p class="text-gray-600">{{ order.city }}, {{ order.governorate }}</p>
                {% if order.postal_code %}
                <p class="text-gray-600">{{ order.postal_code }}</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- تحديثات مفصلة -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 class="text-xl font-bold text-gray-900 mb-6">سجل التحديثات</h3>
        <div class="space-y-4">
            {% for update in tracking.updates.all %}
            <div class="border-r-4 border-blue-500 pr-4 py-3 bg-gray-50 rounded">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold text-gray-900">{{ update.get_status_display }}</h4>
                    <span class="text-sm text-gray-500">{{ update.timestamp|date:"Y/m/d H:i" }}</span>
                </div>
                {% if update.location %}
                <p class="text-sm text-gray-600 mb-1">
                    <i class="fas fa-map-marker-alt ml-1 text-red-500"></i>
                    {{ update.location }}
                </p>
                {% endif %}
                <p class="text-gray-700">{{ update.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
    
    {% if tracking.notes %}
    <!-- ملاحظات -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">ملاحظات خاصة</h3>
        <p class="text-yellow-700">{{ tracking.notes }}</p>
    </div>
    {% endif %}
    
    <!-- أزرار الإجراءات -->
    <div class="flex flex-wrap gap-4 mt-6">
        <a href="{% url 'orders:order_list' %}" 
           class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition duration-300">
            العودة لقائمة الطلبات
        </a>
        
        {% if tracking.current_status != 'delivered' %}
        <button onclick="window.print()" 
                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
            <i class="fas fa-print ml-2"></i>
            طباعة معلومات التتبع
        </button>
        {% endif %}
        
        <a href="mailto:<EMAIL>?subject=استفسار عن الطلب {{ order.order_number }}" 
           class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition duration-300">
            <i class="fas fa-envelope ml-2"></i>
            تواصل مع الدعم
        </a>
    </div>
</div>
{% endblock %}
