from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from orders.models import Order, OrderItem, ShippingTracking, TrackingUpdate
from products.models import Product
from datetime import datetime, timedelta
import random

class Command(BaseCommand):
    help = 'إنشاء طلبات تجريبية مع تتبع الشحن'

    def handle(self, *args, **options):
        # إنشاء مستخدم تجريبي إذا لم يكن موجوداً
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'أحمد',
                'last_name': 'محمد'
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
            self.stdout.write('تم إنشاء مستخدم تجريبي: testuser')

        # الحصول على بعض المنتجات
        products = list(Product.objects.filter(is_available=True)[:5])
        if not products:
            self.stdout.write(self.style.ERROR('لا توجد منتجات متاحة'))
            return

        # إنشاء طلبات تجريبية
        statuses = ['confirmed', 'processing', 'shipped', 'in_transit', 'delivered']
        
        for i in range(3):
            # إنشاء الطلب
            order = Order.objects.create(
                user=user,
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                phone='01234567890',
                address_line_1='شارع التحرير 123',
                city='القاهرة',
                governorate='القاهرة',
                subtotal=random.randint(200, 1000),
                shipping_cost=50,
                total=random.randint(250, 1050),
                status=random.choice(statuses)
            )

            # إضافة منتجات للطلب
            selected_products = random.sample(products, random.randint(1, 3))
            for product in selected_products:
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    quantity=random.randint(1, 3),
                    price=product.get_price
                )

            # إنشاء تتبع الشحن
            tracking = ShippingTracking.objects.create(
                order=order,
                current_status=random.choice(['order_placed', 'preparing', 'shipped', 'in_transit']),
                estimated_delivery=datetime.now() + timedelta(days=random.randint(1, 5))
            )

            # إضافة تحديثات التتبع
            tracking_statuses = [
                ('order_placed', 'القاهرة - مركز التوزيع الرئيسي', 'تم تأكيد الطلب وبدء عملية التجهيز'),
                ('preparing', 'القاهرة - مستودع التجهيز', 'جاري تجهيز المنتجات وتغليفها'),
                ('shipped', 'القاهرة - مركز الشحن', 'تم شحن الطلب وهو في طريقه إليك'),
                ('in_transit', 'في الطريق', 'الطلب في طريقه للتسليم'),
            ]

            for j, (status, location, description) in enumerate(tracking_statuses):
                if j <= statuses.index(tracking.current_status):
                    TrackingUpdate.objects.create(
                        tracking=tracking,
                        status=status,
                        location=location,
                        description=description
                    )

            self.stdout.write(f'تم إنشاء طلب #{order.order_number} مع تتبع {tracking.tracking_number}')

        self.stdout.write(
            self.style.SUCCESS('تم إنشاء الطلبات التجريبية بنجاح!')
        )
